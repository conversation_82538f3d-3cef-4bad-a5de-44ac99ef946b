const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  // [Web-only]: Enables CSS support in Metro.
  isCSSEnabled: true,
});

// <PERSON><PERSON><PERSON> requires these extensions to be added to Metro
config.resolver.sourceExts.push('mjs');

// Add support for .web.js files for better web compatibility
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;
