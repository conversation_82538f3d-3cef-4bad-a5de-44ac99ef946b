import { config } from '@tamagui/config/v3'
import { createTamagui } from 'tamagui'
import { createAnimations } from '@tamagui/animations-moti'

const animations = createAnimations({
  bouncy: {
    type: 'spring',
    damping: 10,
    mass: 0.9,
    stiffness: 100,
  },
  lazy: {
    type: 'spring',
    damping: 20,
    stiffness: 60,
  },
  quick: {
    type: 'spring',
    damping: 20,
    mass: 1.2,
    stiffness: 250,
  },
  smooth: {
    type: 'timing',
    duration: 300,
  },
  slow: {
    type: 'timing',
    duration: 500,
  },
})

// You can customize the config here or use the default
const appConfig = createTamagui({
  ...config,
  animations,
  tokens: {
    ...config.tokens,
    color: {
      ...config.tokens.color,
      "bg-primary": '#101010',
    },
  },
})

export default appConfig

export type Conf = typeof appConfig

declare module 'tamagui' {
  interface TamaguiCustomConfig extends Conf { }
}
